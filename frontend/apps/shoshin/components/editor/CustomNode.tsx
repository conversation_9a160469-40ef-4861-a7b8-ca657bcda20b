"use client"

import { <PERSON><PERSON>, Position, type NodeProps } from "@xyflow/react"
import {
    Brain,
    Code,
    Database,
    GitBranch,
    Globe,
    MessageSquare,
    Play,
    RotateCcw,
    Route,
    User,
    Workflow
} from "lucide-react"

const iconMap = {
  agent: User,
  api: Globe,
  condition: GitBranch,
  function: Code,
  router: Route,
  memory: Database,
  knowledge: Brain,
  workflow: Workflow,
  response: MessageSquare,
  loop: RotateCcw,
  start: Play,
}

const colorMap = {
  agent: "bg-primary-500",
  api: "bg-accent-500",
  condition: "bg-orange-500",
  function: "bg-secondary-500",
  router: "bg-green-500",
  memory: "bg-pink-500",
  knowledge: "bg-teal-500",
  workflow: "bg-amber-500",
  response: "bg-blue-500",
  loop: "bg-cyan-500",
  start: "bg-neutral-600",
}

interface CustomNodeData {
  label: string
  type: string
  description?: string
}

export function CustomNode({ data, selected }: NodeProps<CustomNodeData>) {
  const Icon = iconMap[data.type as keyof typeof iconMap] || Play
  const bgColor = colorMap[data.type as keyof typeof colorMap] || "bg-neutral-600"

  return (
    <div className={`group px-4 py-3 rounded-md bg-background border-2 min-w-[240px] transition-all duration-300 ${
      selected
        ? "border-primary-500 shadow-lg shadow-primary-500/20"
        : "border-border hover:border-primary-500/50 hover:shadow-md"
    }`}>
      <Handle
        type="target"
        position={Position.Left}
        className="!w-[7px] !h-5 !bg-slate-300 dark:!bg-slate-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(156,163,175,0.15)] hover:!w-[10px] hover:!left-[-10px] hover:!rounded-l-full hover:!rounded-r-none !cursor-crosshair transition-[colors] duration-150 !left-[-7px]"
        style={{
          top: '50%',
          transform: 'translateY(-50%)',
        }}
      />

      <div className="flex items-center space-x-3">
        <div className={`w-10 h-10 ${bgColor} rounded-md flex items-center justify-center flex-shrink-0 shadow-sm`}>
          <Icon className="w-5 h-5 text-white" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="text-sm font-semibold text-foreground truncate">
            {data.label}
          </div>
          {data.description && (
            <div className="text-xs text-muted-foreground truncate mt-0.5">
              {data.description}
            </div>
          )}
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Right}
        className="!w-[7px] !h-5 !bg-slate-300 dark:!bg-slate-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(156,163,175,0.15)] hover:!w-[10px] hover:!right-[-10px] hover:!rounded-r-full hover:!rounded-l-none !cursor-crosshair transition-[colors] duration-150 !right-[-7px]"
        style={{
          top: '50%',
          transform: 'translateY(-50%)',
        }}
      />
    </div>
  )
}
